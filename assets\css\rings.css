/* === SECCIÓN MANO - rings.css === */
.ring-section {
  padding: 60px 0;
  text-align: center;
}

svg#hand-svg {
  max-width: 350px;
  width: 70%;
  height: auto;
  margin-top: 20px;
}

#hand-outline {
  stroke: #333;
  stroke-width: 2.5;
  fill: none;
  transition: stroke .3s;
}

svg#hand-svg:hover #hand-outline {
  stroke: #000;
}

.finger rect {
  cursor: pointer;
}

.ring {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity .3s ease, transform .3s ease;
  pointer-events: none;
  /* Para que no interfiera con el hover de los dedos */
}

.ring.visible {
  opacity: 1;
  transform: translateY(0);
}