// hand.js

// Inicializa Tippy.js en todos los elementos .finger
document.addEventListener('DOMContentLoaded', () => {
  // Configuración de Tippy.js para los tooltips
  tippy('.finger', {
    theme: 'light-border',
    animation: 'scale',
    delay: [100, 50],
    placement: 'auto',
    arrow: true,
    maxWidth: 220,
  });

  // Lógica para mostrar/ocultar anillos
  const fingers = document.querySelectorAll('.finger');
  fingers.forEach(finger => {
    const ringId = finger.getAttribute('data-ring');
    const ring = document.getElementById(ringId);

    if (ring) {
      finger.addEventListener('mouseenter', () => {
        ring.classList.add('visible');
      });
      finger.addEventListener('mouseleave', () => {
        ring.classList.remove('visible');
      });
    }
  });
});
